//
//  TrialReminderTestView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import SwiftUI

/**
 * 试用期订阅提醒功能测试视图
 * 用于验证温馨提醒弹窗功能是否正常工作
 */
struct TrialReminderTestView: View {
    
    // MARK: - State
    @State private var showTrialReminderModal = false
    @State private var showSubscriptionView = false
    @StateObject private var trialManager = TrialManager.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                
                // 标题
                Text("试用期订阅提醒功能测试")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top, 20)
                
                // 当前试用状态
                VStack(spacing: 10) {
                    Text("当前试用状态")
                        .font(.headline)
                    
                    Text("试用是否激活: \(trialManager.isTrialActive ? "是" : "否")")
                        .foregroundColor(trialManager.isTrialActive ? .green : .red)
                    
                    if trialManager.isTrialActive {
                        Text("剩余天数: \(trialManager.getRemainingTrialDays()) 天")
                            .foregroundColor(.blue)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                // 测试按钮组
                VStack(spacing: 15) {
                    
                    // 模拟点击查看订阅方案按钮
                    Button(action: {
                        handleViewPlansPressed()
                    }) {
                        Text("模拟点击「查看订阅方案」")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(DesignSystem.Colors.primary)
                            .cornerRadius(12)
                    }
                    
                    // 直接显示提醒弹窗
                    Button(action: {
                        showTrialReminderModal = true
                    }) {
                        Text("直接显示温馨提醒弹窗")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.orange)
                            .cornerRadius(12)
                    }
                    
                    // 模拟领取试用
                    Button(action: {
                        Task {
                            let success = await trialManager.claimTrial()
                            if success {
                                print("✅ 试用领取成功")
                            }
                        }
                    }) {
                        Text("模拟领取试用")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.green)
                            .cornerRadius(12)
                    }
                    .disabled(trialManager.hasReceivedTrial)
                    
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 说明文字
                Text("说明：当用户处于试用期内时，点击「查看订阅方案」会先显示温馨提醒弹窗")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
            }
            .navigationTitle("试用提醒测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        // 试用期订阅提醒弹窗覆盖层
        .overlay(
            showTrialReminderModal ?
            TrialSubscriptionReminderModal(isPresented: $showTrialReminderModal) {
                // 用户点击"谢谢提醒"后，关闭弹窗并跳转到订阅页面
                print("🔔 用户确认试用期提醒，跳转到订阅页面")
                showSubscriptionView = true
            } : nil
        )
        .sheet(isPresented: $showSubscriptionView) {
            // 这里可以显示订阅页面，暂时用简单的文本代替
            NavigationView {
                VStack {
                    Text("订阅页面")
                        .font(.title)
                        .padding()
                    
                    Text("这里是订阅页面的内容")
                        .foregroundColor(.gray)
                    
                    Spacer()
                    
                    Button("关闭") {
                        showSubscriptionView = false
                    }
                    .padding()
                }
                .navigationTitle("订阅")
                .navigationBarTitleDisplayMode(.inline)
            }
        }
    }
    
    // MARK: - Methods
    
    /**
     * 处理查看会员方案按钮点击
     * 复制自ProfileView的逻辑
     */
    private func handleViewPlansPressed() {
        print("查看会员方案功能")

        // 检查用户是否处于试用期内
        if trialManager.isTrialActive {
            print("🔔 用户处于试用期内，显示温馨提醒")
            // 显示试用期提醒弹窗
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showTrialReminderModal = true
            }
        } else {
            // 直接跳转到订阅页面
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showSubscriptionView = true
            }
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Preview
#Preview {
    TrialReminderTestView()
        .background(DesignSystem.Colors.background)
}
